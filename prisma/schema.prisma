generator client {
  provider = "prisma-client-js"
  output   = "../node_modules/.prisma/client"
  binaryTargets = ["native", "rhel-openssl-3.0.x"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
}

model User {
  id              String           @id @default(uuid())
  account         String           @unique
  name            String?
  passwordHash    String?          @map("password_hash")
  lastLogin       DateTime?        @map("last_login")
  lastLogout      DateTime?        @map("last_logout")
  faceSources     FaceSource[]
  generatedMedia  GeneratedMedia[]
  Payment         Payment[]
  targetTemplates TargetTemplate[]
}

model TargetTemplate {
  id             String           @id @default(uuid())
  filename       String
  type           String
  filePath       String           @map("file_path")
  thumbnailPath  String?          @map("thumbnail_path")
  fileSize       BigInt           @map("file_size")
  duration       Int?
  mimeType       String?          @map("mime_type")
  usageCount     Int              @default(0) @map("usage_count")
  createdAt      DateTime         @default(now()) @map("created_at")
  lastUsedAt     DateTime?        @map("last_used_at")
  isActive       Boolean          @default(true) @map("is_active")
  authorId       String?          @map("author_id")
  generatedMedia GeneratedMedia[]
  author         User?            @relation(fields: [authorId], references: [id])
}

model FaceSource {
  id             String           @id @default(uuid())
  filename       String
  width          Int
  height         Int
  filePath       String           @map("file_path")
  fileSize       BigInt           @map("file_size")
  mimeType       String           @map("mime_type")
  createdAt      DateTime         @default(now()) @map("created_at")
  lastUsedAt     DateTime?        @map("last_used_at")
  usageCount     Int              @default(0) @map("usage_count")
  isActive       Boolean          @default(true) @map("is_active")
  authorId       String?          @map("author_id")
  author         User?            @relation(fields: [authorId], references: [id])
  generatedMedia GeneratedMedia[]
}

model GeneratedMedia {
  id             String          @id @default(uuid())
  name           String
  type           String
  tempPath       String?         @map("temp_path")
  filePath       String          @map("file_path")
  fileSize       BigInt          @map("file_size")
  mimeType       String          @map("mime_type")
  createdAt      DateTime        @default(now()) @map("created_at")
  downloadCount  Int             @default(0) @map("download_count")
  isPaid         Boolean         @default(false)
  isActive       Boolean         @default(true) @map("is_active")
  authorId       String?         @map("author_id")
  templateId     String?         @map("template_id")
  faceSourceId   String?         @map("face_source_id")
  author         User?           @relation(fields: [authorId], references: [id])
  faceSource     FaceSource?     @relation(fields: [faceSourceId], references: [id])
  targetTemplate TargetTemplate? @relation(fields: [templateId], references: [id])
}

model Guideline {
  id        String   @id @default(uuid())
  filename  String
  width     Int
  height    Int
  fileType  String   @map("file_type")
  fileSize  BigInt   @map("file_size")
  filePath  String   @map("file_path")
  isAllowed Boolean  @default(true) @map("is_allowed")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")
}

model Payment {
  id               String   @id @default(uuid())
  amount           Decimal
  currency         String
  status           String
  type             String
  txHash           String?
  createdAt        DateTime @default(now())
  userId           String
  generatedMediaId String
  user             User     @relation(fields: [userId], references: [id])
}
